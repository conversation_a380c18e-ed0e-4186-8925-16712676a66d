# Product Overview

Element Server Suite Community Edition (ESS Community) is a Helm chart for deploying a complete Matrix stack on Kubernetes. It's the official Matrix distribution from Element for non-commercial use cases.

## Core Components

- **Synapse**: The Matrix homeserver
- **Matrix Authentication Service (MAS)**: User management and authentication
- **Element Web**: Web-based Matrix client
- **Matrix RTC Backend**: Real-time communication for Element Call
- **PostgreSQL**: Database (optional, can use external)
- **HAProxy**: Load balancer and routing
- **Well-known delegation**: Federation support

## Target Use Cases

- Small to mid-scale non-commercial Matrix deployments
- Community servers and organizations
- Quick Matrix stack deployment with minimal Kubernetes knowledge
- Matrix 2.0 compliant installations

## License

AGPLv3 for community use. Commercial versions (ESS Pro, ESS TI-M) available separately.