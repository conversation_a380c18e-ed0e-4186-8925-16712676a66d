# Technology Stack

## Core Technologies

- **Helm**: Kubernetes package manager for chart deployment
- **Kubernetes**: Container orchestration platform (K3s recommended for simple setups)
- **Python 3.12+**: Build tooling and test automation
- **Go**: Matrix-tools utility container
- **Jinja2**: Template engine for generating Helm values and schemas
- **Docker**: Container images and multi-stage builds

## Build System

### Python Environment
- **Poetry**: Dependency management and virtual environments
- **pyproject.toml**: Project configuration and dependencies

### Chart Generation
Charts are generated from source templates, not edited directly:
- `charts/matrix-stack/source/` contains Jinja2 templates
- `scripts/construct_helm_values.py` generates `values.yaml`
- `scripts/construct_helm_schema.py` generates `values.schema.json`

### Testing Framework
- **pytest**: Python testing with Kubernetes integration
- **Chart Testing (ct)**: Helm chart linting and testing
- **Checkov**: Security and compliance scanning

## Common Commands

### Development Setup
```bash
# Install dependencies
poetry install

# Generate chart files from templates
poetry run python scripts/construct_helm_values.py charts/matrix-stack/source/values.yaml.j2 charts/matrix-stack/values.yaml
poetry run python scripts/construct_helm_schema.py charts/matrix-stack/source/values.schema.json charts/matrix-stack/values.schema.json

# Lint charts
poetry run scripts/ct-lint.sh

# Run security checks
poetry run scripts/checkov.sh
```

### Testing
```bash
# Run manifest tests
poetry run pytest tests/manifests/

# Run integration tests (requires cluster)
poetry run pytest tests/integration/
```

### Container Builds
```bash
# Build matrix-tools container
docker buildx bake matrix-tools
```

## Key Constraints

- **Never edit generated files directly**: Always modify source templates in `source/` directory
- **Schema validation**: All values must conform to JSON schema
- **Kubernetes compatibility**: Support for various K8s distributions (K3s, microk8s, etc.)
- **Airgapped environments**: No external dependencies during runtime