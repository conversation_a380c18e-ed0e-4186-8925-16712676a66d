# Project Structure

## Repository Layout

### Core Chart Directory
- `charts/matrix-stack/` - Main Helm chart
  - `source/` - **Source templates** (edit these, not generated files)
    - `values.yaml.j2` - Values template
    - `values.schema.json` - Schema source
    - `*.json` - Component schema definitions
    - `*.yaml.j2` - Component template files
  - `templates/` - Generated Kubernetes manifests
  - `configs/` - Configuration file templates for services
  - `ci/` - CI test values and fragments
  - `values.yaml` - **Generated** (do not edit directly)
  - `values.schema.json` - **Generated** (do not edit directly)

### Build and Tooling
- `scripts/` - Build scripts and utilities
  - `construct_helm_*.py` - Chart generation scripts
  - `*.sh` - Shell scripts for testing and CI
- `matrix-tools/` - Go utility container source
- `tests/` - Test suites
  - `manifests/` - Helm template tests
  - `integration/` - End-to-end Kubernetes tests

### Documentation
- `docs/` - User documentation
- `README.md` - Main setup guide
- `CHANGELOG.md` - Release notes

### Configuration Management
- `pyproject.toml` - Python project configuration
- `poetry.lock` - Dependency lock file
- `ct.yaml` - Chart testing configuration
- `docker-bake.hcl` - Container build configuration

## Component Organization

Each Matrix component follows a consistent pattern:
- Schema definition in `source/componentName.json`
- Template in `source/componentName.yaml.j2`
- Kubernetes manifests in `templates/componentName/`
- Configuration templates in `configs/componentName/`

## File Naming Conventions

- **Templates**: `.yaml.j2` extension for Jinja2 templates
- **Schemas**: `.json` files for JSON schema definitions
- **Generated files**: No special extension, but marked as generated
- **CI values**: Descriptive names in `ci/` directory
- **Fragments**: Reusable configuration pieces in `ci/fragments/`

## Key Principles

- **Source of truth**: Always in `source/` directory
- **Generated artifacts**: Never edit `values.yaml` or `values.schema.json` directly
- **Modular design**: Each component can be enabled/disabled independently
- **Configuration layering**: Base values + fragments + user overrides
- **Test coverage**: Both unit tests for templates and integration tests for deployments