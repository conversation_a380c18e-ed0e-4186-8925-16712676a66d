# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

name: dyff of rendered templates - comment
on:
  workflow_run:
    workflows: ["dyff of rendered templates"]
    types:
    - completed

permissions:
  actions: read
  pull-requests: write

jobs:
  manage-comment:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'
    steps:
    - name: Download dyff of templates
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093  # v4
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        name: dyff-templates
        run-id: ${{ github.event.workflow_run.id }}

    - name: Load PR number
      id: pr-number
      run: |
        # This is already formatted as pr-number=<pr number>
        cat "pr-number.txt" >> "$GITHUB_OUTPUT"

    - name: Find dyff comment
      uses: peter-evans/find-comment@3eae4d37986fb5a8592848f6a574fdf654e61f9e  # v3
      id: find-dyff-comment
      with:
        issue-number: ${{ steps.pr-number.outputs.pr-number }}
        comment-author: 'github-actions[bot]'
        body-includes: 'dyff of changes in rendered templates'

    - name: Create or update comment
      uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043  # v4
      with:
        comment-id: ${{ steps.find-dyff-comment.outputs.comment-id }}
        issue-number: ${{ steps.pr-number.outputs.pr-number }}
        body-path: dyff-output.md
        edit-mode: replace
