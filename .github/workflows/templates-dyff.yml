# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

name: dyff of rendered templates
on:
  pull_request:

permissions:
  contents: read

jobs:
  generate-dyff:
    runs-on: ubuntu-latest
    steps:
    # This checks out the merge of the PR branch and the base branch.
    # If you check this to be just the PR branch then the ref for the subsequent checkout
    # needs to be changed too or you end up with unexpected changes being shown.
    - name: Checkout PR
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        fetch-depth: 0

    # helm template doesn't reliably order manifests within the same kind, so use yq to do it for us
    - name: Generate manifests for PR
      id: generate-manifests
      run: |
        mkdir -p "$RUNNER_TEMP/new"
        for values in charts/matrix-stack/ci/*values.yaml; do
          echo "Generating new templates with $values";
          mkdir -p "$RUNNER_TEMP/new/$(basename "$values" ".yaml")"
          helm template \
              -n ess-ci \
              -a monitoring.coreos.com/v1/ServiceMonitor \
              -f "$values" charts/matrix-stack | \
          yq ea '[.] | .[] | splitDoc' | \
          yq -s "\"$RUNNER_TEMP/new/$(basename "$values" ".yaml")/\""' + ([.kind, .metadata.name] | join("-") | downcase) + ".yaml"'
        done
        echo "output_dir=$RUNNER_TEMP/new" | tee -a "$GITHUB_OUTPUT"

    # https://github.com/orgs/community/discussions/59677 says that github.event.pull_request.base.sha
    # is only calculated on creation of the PR, so we reference the target branch by ref.
    - name: Checkout target
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.base.ref }}

    - name: Generate manifests for base
      run: |
        mkdir -p "$RUNNER_TEMP/old"
        for values in charts/matrix-stack/ci/*values.yaml; do
          echo "Generating old templates with $values";
          mkdir -p "$RUNNER_TEMP/old/$(basename "$values" ".yaml")"
          helm template \
              -n ess-ci \
              -a monitoring.coreos.com/v1/ServiceMonitor \
              -f "$values" charts/matrix-stack | \
          yq ea '[.] | .[] | splitDoc' | \
          yq -s "\"$RUNNER_TEMP/old/$(basename "$values" ".yaml")/\""' + ([.kind, .metadata.name] | join("-") | downcase) + ".yaml"'
        done

    - name: Install dyff with asdf
      uses: asdf-vm/actions/install@1902764435ca0dd2f3388eea723a4f92a4eb8302  # v4
      with:
        tool_versions: |
          dyff 1.10.1

    - name: Upload new manifests
      id: upload-new
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4
      with:
        name: new-manifests
        path: ${{ steps.generate-manifests.outputs.output_dir }}
        retention-days: 1

    - name: dyff old and new manifests
      id: dyff
      shell: bash
      env:
        ARTIFACT_URL: ${{ steps.upload-new.outputs.artifact-url }}
        PR_NUMBER: ${{ github.event.pull_request.number }}
      run: |
        echo "output_dir=$RUNNER_TEMP" | tee -a "$GITHUB_OUTPUT"
        values_directories=$(find "$RUNNER_TEMP/old" "$RUNNER_TEMP/new" -maxdepth 1 -type d | sed -E 's|'"$RUNNER_TEMP"'/(old\|new)||' | sed -E 's|^/||' | sort | uniq)
        header="# dyff of changes in rendered templates of CI manifests\n\n"
        comment_body=""
        while read -r values_dir; do
          if [ -z "$values_dir" ]; then
            continue
          fi

          templates_files=$(find "$RUNNER_TEMP/old" "$RUNNER_TEMP/new" -maxdepth 2 -name '*.yaml' | grep "/$values_dir/" | sed -E 's|'"$RUNNER_TEMP"'/(old\|new)/||' | sort | uniq)
          comment_templates_body=""
          values_file_suffix=""
          on_both_branches="true"
          if [ ! -d "$RUNNER_TEMP/old/$values_dir" ]; then
            values_file_suffix=" (added)"
            on_both_branches="false"
          elif [ ! -d "$RUNNER_TEMP/new/$values_dir" ]; then
            values_file_suffix=" (removed)"
            on_both_branches="false"
          fi

          while read -r templates_file; do
            current_file="$(basename "$templates_file")"
            if [[ "$current_file" == ".yaml" ]] && [ ! -s "$template_file" ]; then
              continue
            fi

            if [ ! -f "$RUNNER_TEMP/old/$templates_file" ]; then
              kind=$(yq '.kind' "$RUNNER_TEMP/new/$templates_file")
              name=$(yq '.metadata.name' "$RUNNER_TEMP/new/$templates_file")
              namespace=$(yq '.metadata.namespace' "$RUNNER_TEMP/new/$templates_file")
              if [ "$on_both_branches" == "true" ]; then
                comment_templates_body+="@@ $kind/$namespace/$name @@\n"
                while IFS= read -r line; do
                  comment_templates_body+="+ $line\n"
                done < "$RUNNER_TEMP/new/$templates_file"
                comment_templates_body+="\n\n"
              else
                comment_templates_body+="$kind/$namespace/$name added\n"
              fi
              continue
            fi

            if [ ! -f "$RUNNER_TEMP/new/$templates_file" ]; then
              kind=$(yq '.kind' "$RUNNER_TEMP/old/$templates_file")
              name=$(yq '.metadata.name' "$RUNNER_TEMP/old/$templates_file")
              namespace=$(yq '.metadata.namespace' "$RUNNER_TEMP/old/$templates_file")
              if [ "$on_both_branches" == "true" ]; then
                comment_templates_body+="@@ $kind/$namespace/$name @@\n"
                while IFS= read -r line; do
                  comment_templates_body+="- $line\n"
                done < "$RUNNER_TEMP/old/$templates_file"
                comment_templates_body+="\n\n"
              else
                comment_templates_body+="$kind/$namespace/$name removed\n"
              fi
              continue
            fi

            exit_code=0
            dyff_detail=$(dyff between --set-exit-code --omit-header --output=github "$RUNNER_TEMP/old/$templates_file" "$RUNNER_TEMP/new/$templates_file" 2>&1) || exit_code=$?
            if [ $exit_code -ne 0 ]; then
              if [[ "$dyff_detail" == *"failed to compare input files"* ]]; then
                echo "failed with file $templates_file"
                exit 1
              fi

              kind=$(yq '.kind' "$RUNNER_TEMP/new/$templates_file")
              name=$(yq '.metadata.name' "$RUNNER_TEMP/new/$templates_file")
              namespace=$(yq '.metadata.namespace' "$RUNNER_TEMP/new/$templates_file")
              comment_templates_body+=$(sed -e '/^!/d' -e "s|^@@\(.*\)@@|@@ $kind/$namespace/$name -\1@@|" <<< "$dyff_detail")
              comment_templates_body+="\n\n\n"
            fi
          done <<< "$templates_files"

          if [[ -n "$comment_templates_body" ]]; then
              comment_body+="<details><summary><b>$values_dir.yaml$values_file_suffix</b></summary>\n"
              comment_body+='\n```diff\n'
              comment_body+="$comment_templates_body"
              comment_body+='```\n'
              comment_body+="\n</details>\n"
          fi

        done <<< "$values_directories"

        if [ -z "$comment_body" ]; then
          comment_body="No changes in rendered templates"
        else
          header+="Full contents of manifests and dyffs are available in ${ARTIFACT_URL}\n\n"
        fi


        echo -e "$header$comment_body" | tee "$RUNNER_TEMP/dyff-output.md"
        echo "pr-number=$PR_NUMBER" | tee "$RUNNER_TEMP/pr-number.txt"

    - name: Upload generated manifests
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4
      with:
        name: dyff-templates
        path: ${{ steps.dyff.outputs.output_dir }}
        retention-days: 1
