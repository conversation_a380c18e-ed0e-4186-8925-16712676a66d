# Matrix 服务器故障排除指南

## 📋 目录
1. [快速诊断](#快速诊断)
2. [系统环境问题](#系统环境问题)
3. [网络连接问题](#网络连接问题)
4. [证书相关问题](#证书相关问题)
5. [数据库问题](#数据库问题)
6. [容器服务问题](#容器服务问题)
7. [DNS和域名问题](#dns和域名问题)
8. [性能问题](#性能问题)
9. [用户认证问题](#用户认证问题)
10. [日志分析](#日志分析)
11. [紧急恢复](#紧急恢复)

---

## 🔍 快速诊断

### 一键健康检查脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/quick-check.sh

echo "=== Matrix 服务器快速诊断 ==="
echo "时间: $(date)"
echo

# 1. 检查系统资源
echo "📊 系统资源状态:"
echo "内存使用:"
free -h | grep -E "(Mem|Swap)"
echo "磁盘使用:"
df -h /opt/matrix | tail -1
echo "CPU负载:"
uptime
echo

# 2. 检查容器状态
echo "🐳 容器状态:"
containers=("matrix-postgres" "matrix-redis" "matrix-synapse" "matrix-element" "matrix-mas" "matrix-coturn")
for container in "${containers[@]}"; do
    if podman ps --format "{{.Names}}" | grep -q "^${container}$"; then
        echo "✅ $container - 运行中"
    else
        echo "❌ $container - 未运行"
        echo "   最后10行日志:"
        podman logs --tail 10 $container 2>/dev/null | sed 's/^/   /'
    fi
done
echo

# 3. 检查端口监听
echo "🔌 端口监听状态:"
ports=("5432:PostgreSQL" "6379:Redis" "8008:Synapse" "8080:Element" "8081:MAS" "3478:Coturn")
for port_info in "${ports[@]}"; do
    port=$(echo $port_info | cut -d: -f1)
    service=$(echo $port_info | cut -d: -f2)
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        echo "✅ $service ($port) - 监听中"
    else
        echo "❌ $service ($port) - 未监听"
    fi
done
echo

# 4. 检查服务响应
echo "🌐 服务响应测试:"
services=("http://localhost:8008/_matrix/client/versions:Synapse API" 
          "http://localhost:8080:Element Web" 
          "http://localhost:8081:MAS")
for service_info in "${services[@]}"; do
    url=$(echo $service_info | cut -d: -f1-3)
    name=$(echo $service_info | cut -d: -f4)
    if curl -s -o /dev/null -w "%{http_code}" "$url" --connect-timeout 5 | grep -q "200\|404\|302"; then
        echo "✅ $name - 响应正常"
    else
        echo "❌ $name - 无响应"
    fi
done
echo

# 5. 检查DNS解析
echo "🌍 DNS解析测试:"
domains=("matrix.example.com" "chat.example.com" "mas.example.com" "turn.example.com")
for domain in "${domains[@]}"; do
    if nslookup $domain >/dev/null 2>&1; then
        ip=$(nslookup $domain | grep "Address:" | tail -1 | awk '{print $2}')
        echo "✅ $domain -> $ip"
    else
        echo "❌ $domain - 解析失败"
    fi
done
echo

# 6. 检查证书状态
echo "🔒 SSL证书状态:"
if [ -f "/etc/nginx/ssl/wildcard.example.com.crt" ]; then
    expiry=$(openssl x509 -in /etc/nginx/ssl/wildcard.example.com.crt -noout -enddate | cut -d= -f2)
    echo "✅ 通配符证书过期时间: $expiry"
else
    echo "❌ 通配符证书文件不存在"
fi

echo
echo "=== 诊断完成 ==="
```

### 使用方法
```bash
# 创建并运行诊断脚本
chmod +x /opt/matrix/scripts/quick-check.sh
/opt/matrix/scripts/quick-check.sh
```

---

## 💻 系统环境问题

### 问题1：系统内存不足

**症状**：
- 容器频繁重启
- 服务响应缓慢
- 出现 "Out of Memory" 错误

**诊断命令**：
```bash
# 检查内存使用
free -h
# 检查内存占用最高的进程
ps aux --sort=-%mem | head -10
# 检查系统日志中的OOM记录
dmesg | grep -i "killed process"
```

**解决方案**：
```bash
# 1. 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# 2. 优化容器内存限制
podman run --memory=1g --memory-swap=2g [其他参数]

# 3. 清理系统缓存
sudo sync && sudo sysctl vm.drop_caches=3
```

### 问题2：磁盘空间不足

**症状**：
- 容器无法启动
- 数据库写入失败
- 日志显示 "No space left on device"

**诊断命令**：
```bash
# 检查磁盘使用
df -h
# 查找大文件
find /opt/matrix -type f -size +100M -exec ls -lh {} \;
# 检查日志文件大小
du -sh /opt/matrix/logs/*
```

**解决方案**：
```bash
# 1. 清理容器日志
podman system prune -f
podman volume prune -f

# 2. 清理旧的备份文件
find /opt/matrix/backups -name "*.tar.gz" -mtime +7 -delete

# 3. 压缩日志文件
gzip /opt/matrix/logs/*.log

# 4. 清理系统日志
sudo journalctl --vacuum-time=7d
```

### 问题3：权限问题

**症状**：
- 容器无法访问挂载的目录
- 配置文件无法读取
- 数据无法写入

**诊断命令**：
```bash
# 检查目录权限
ls -la /opt/matrix/
# 检查文件所有者
ls -la /opt/matrix/configs/
# 检查SELinux状态（如果启用）
getenforce
```

**解决方案**：
```bash
# 1. 修复目录权限
sudo chown -R matrix:matrix /opt/matrix/
sudo chmod -R 755 /opt/matrix/configs/
sudo chmod -R 750 /opt/matrix/data/

# 2. 设置SELinux上下文（如果需要）
sudo setsebool -P container_manage_cgroup on
sudo semanage fcontext -a -t container_file_t "/opt/matrix(/.*)?"
sudo restorecon -R /opt/matrix/

# 3. 为Podman设置正确的权限
sudo usermod -aG wheel matrix
```

---

## 🌐 网络连接问题

### 问题1：外部无法访问服务

**症状**：
- 浏览器显示连接超时
- curl命令无响应
- 客户端无法连接

**诊断命令**：
```bash
# 检查端口监听
netstat -tlnp | grep -E "(8008|8080|8081|3478)"
# 检查防火墙状态
sudo ufw status
# 测试本地连接
curl -v http://localhost:8008/_matrix/client/versions
```

**解决方案**：
```bash
# 1. 开放防火墙端口
sudo ufw allow 8008/tcp
sudo ufw allow 8080/tcp
sudo ufw allow 8081/tcp
sudo ufw allow 3478/tcp
sudo ufw allow 3478/udp

# 2. 检查容器网络配置
podman inspect matrix-synapse | grep -A 10 "NetworkSettings"

# 3. 重启网络服务
sudo systemctl restart networking
```

### 问题2：容器间网络通信失败

**症状**：
- Synapse无法连接数据库
- 服务间API调用失败
- 内部服务解析失败

**诊断命令**：
```bash
# 测试容器间连接
podman exec matrix-synapse ping -c 3 localhost
podman exec matrix-synapse nc -zv localhost 5432
# 检查容器网络
podman network ls
```

**解决方案**：
```bash
# 1. 重新创建网络
podman network rm matrix-network 2>/dev/null
podman network create matrix-network

# 2. 重新启动容器并加入网络
podman run --network matrix-network [其他参数]

# 3. 使用host网络模式（临时解决）
podman run --network host [其他参数]
```

### 问题3：RouterOS API连接失败

**症状**：
- IP更新脚本报错
- 无法获取WAN接口IP
- DNS记录未更新

**诊断命令**：
```bash
# 测试RouterOS连接
ping -c 3 ***********
# 测试API端口
nc -zv *********** 8728
# 检查更新日志
tail -f /opt/matrix/logs/ip-update.log
```

**解决方案**：
```bash
# 1. 检查RouterOS API配置
# 登录RouterOS管理界面，确保API服务已启用

# 2. 修改连接参数
nano /opt/matrix/scripts/update-ip.py
# 调整ROUTEROS_HOST、ROUTEROS_USER、ROUTEROS_PASS

# 3. 手动测试API连接
python3 -c "
import routeros_api
try:
    connection = routeros_api.RouterOsApi('***********', username='api', password='api')
    print('连接成功')
    connection.disconnect()
except Exception as e:
    print(f'连接失败: {e}')
"
```

---

## 🔒 证书相关问题

### 问题1：SSL证书申请失败

**症状**：
- acme.sh报错
- Cloudflare API调用失败
- 证书文件不存在

**诊断命令**：
```bash
# 检查acme.sh状态
~/.acme.sh/acme.sh --list
# 测试Cloudflare API
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
     -H "Authorization: Bearer YOUR_CLOUDFLARE_API_TOKEN"
# 检查DNS记录
nslookup example.com
```

**解决方案**：
```bash
# 1. 重新设置Cloudflare API Token
export CF_Token="YOUR_NEW_CLOUDFLARE_API_TOKEN"

# 2. 清理失败的证书申请
~/.acme.sh/acme.sh --remove -d "*.example.com"

# 3. 重新申请证书
~/.acme.sh/acme.sh --issue -d "*.example.com" --dns dns_cf --debug 2

# 4. 检查域名解析是否正确
dig TXT _acme-challenge.example.com
```

### 问题2：证书过期

**症状**：
- 浏览器显示证书过期警告
- 客户端连接失败
- SSL握手错误

**诊断命令**：
```bash
# 检查证书过期时间
openssl x509 -in /etc/nginx/ssl/wildcard.example.com.crt -noout -enddate
# 检查自动续期任务
crontab -l | grep acme
# 查看续期日志
~/.acme.sh/acme.sh --list
```

**解决方案**：
```bash
# 1. 手动续期证书
~/.acme.sh/acme.sh --renew -d "*.example.com" --force

# 2. 重新安装证书到Nginx
~/.acme.sh/acme.sh --install-cert -d "*.example.com" \
  --key-file /etc/nginx/ssl/wildcard.example.com.key \
  --fullchain-file /etc/nginx/ssl/wildcard.example.com.crt \
  --reloadcmd "systemctl reload nginx"

# 3. 重启Nginx
sudo systemctl restart nginx

# 4. 验证证书
curl -vI https://matrix.example.com 2>&1 | grep -E "(expire|valid)"
```

### 问题3：证书权限问题

**症状**：
- Nginx无法读取证书文件
- 权限被拒绝错误
- SSL服务无法启动

**解决方案**：
```bash
# 1. 修复证书文件权限
sudo chown root:root /etc/nginx/ssl/*
sudo chmod 644 /etc/nginx/ssl/*.crt
sudo chmod 600 /etc/nginx/ssl/*.key

# 2. 检查Nginx配置
sudo nginx -t

# 3. 重启Nginx
sudo systemctl restart nginx
```

---

## 🗄️ 数据库问题

### 问题1：PostgreSQL无法启动

**症状**：
- 容器启动失败
- 数据库连接被拒绝
- 初始化脚本执行失败

**诊断命令**：
```bash
# 检查容器日志
podman logs matrix-postgres
# 检查数据目录权限
ls -la /opt/matrix/data/postgres/
# 测试端口连接
nc -zv localhost 5432
```

**解决方案**：
```bash
# 1. 清理损坏的数据目录
sudo rm -rf /opt/matrix/data/postgres/*

# 2. 重新创建PostgreSQL容器
podman rm -f matrix-postgres
podman run -d \
    --name matrix-postgres \
    --network host \
    -e POSTGRES_PASSWORD="${POSTGRES_PASSWORD}" \
    -e POSTGRES_DB=postgres \
    -v /opt/matrix/data/postgres:/var/lib/postgresql/data:Z \
    --restart unless-stopped \
    docker.io/postgres:15-alpine

# 3. 等待启动并重新初始化数据库
sleep 30
# 重新执行数据库初始化脚本
```

### 问题2：数据库连接失败

**症状**：
- Synapse无法连接数据库
- 连接超时或被拒绝
- 认证失败

**诊断命令**：
```bash
# 测试数据库连接
podman exec -it matrix-postgres psql -U postgres -c "\l"
# 检查用户权限
podman exec -it matrix-postgres psql -U postgres -c "\du"
# 测试Synapse用户连接
podman exec -it matrix-postgres psql -U synapse_user -d synapse -c "SELECT 1;"
```

**解决方案**：
```bash
# 1. 重置数据库用户密码
podman exec -it matrix-postgres psql -U postgres << EOF
ALTER USER synapse_user PASSWORD '${SYNAPSE_DB_PASSWORD}';
ALTER USER mas_user PASSWORD '${MAS_DB_PASSWORD}';
EOF

# 2. 检查Synapse配置中的数据库连接字符串
nano /opt/matrix/configs/synapse/homeserver.yaml

# 3. 重启Synapse容器
podman restart matrix-synapse
```

### 问题3：数据库性能问题

**症状**：
- 查询响应缓慢
- 高CPU使用率
- 连接池耗尽

**诊断命令**：
```bash
# 检查数据库连接数
podman exec -it matrix-postgres psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"
# 检查慢查询
podman exec -it matrix-postgres psql -U postgres -c "SELECT query, calls, total_time FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"
```

**解决方案**：
```bash
# 1. 优化PostgreSQL配置
cat >> /opt/matrix/configs/postgres/postgresql.conf << EOF
# 性能优化配置
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
EOF

# 2. 重启PostgreSQL
podman restart matrix-postgres

# 3. 运行数据库维护
podman exec -it matrix-postgres psql -U postgres -d synapse -c "VACUUM ANALYZE;"
```

---

## 🐳 容器服务问题

### 问题1：Synapse容器启动失败

**症状**：
- 容器立即退出
- 配置文件错误
- 依赖服务不可用

**诊断命令**：
```bash
# 检查容器日志
podman logs matrix-synapse
# 验证配置文件
podman run --rm -v /opt/matrix/configs/synapse:/data:Z \
    docker.io/matrixdotorg/synapse:latest \
    -c /data/homeserver.yaml --generate-keys-only
```

**解决方案**：
```bash
# 1. 检查配置文件语法
python3 -c "
import yaml
with open('/opt/matrix/configs/synapse/homeserver.yaml', 'r') as f:
    try:
        yaml.safe_load(f)
        print('配置文件语法正确')
    except yaml.YAMLError as e:
        print(f'配置文件语法错误: {e}')
"

# 2. 重新生成签名密钥
podman run --rm -v /opt/matrix/configs/synapse:/data:Z \
    docker.io/matrixdotorg/synapse:latest \
    generate-keys -c /data/homeserver.yaml

# 3. 确保数据库已启动
podman start matrix-postgres
sleep 10
podman restart matrix-synapse
```

### 问题2：Element Web无法加载

**症状**：
- 页面显示空白
- 配置文件加载失败
- 无法连接到Matrix服务器

**诊断命令**：
```bash
# 检查Element容器状态
podman logs matrix-element
# 测试配置文件
curl -s http://localhost:8080/config.json | jq .
# 检查网络连接
curl -s http://localhost:8080/
```

**解决方案**：
```bash
# 1. 验证配置文件格式
jq . /opt/matrix/configs/element/config.json

# 2. 重新创建Element容器
podman rm -f matrix-element
podman run -d \
    --name matrix-element \
    --network host \
    -v /opt/matrix/configs/element/config.json:/app/config.json:Z \
    --restart unless-stopped \
    docker.io/vectorim/element-web:latest

# 3. 检查配置文件挂载
podman exec matrix-element cat /app/config.json
```

### 问题3：MAS认证服务问题

**症状**：
- 用户无法登录
- 认证重定向失败
- OAuth流程中断

**诊断命令**：
```bash
# 检查MAS日志
podman logs matrix-mas
# 测试MAS API
curl -s http://localhost:8081/.well-known/openid_configuration
# 检查数据库连接
podman exec -it matrix-postgres psql -U mas_user -d mas -c "SELECT 1;"
```

**解决方案**：
```bash
# 1. 检查MAS配置
cat /opt/matrix/configs/mas/config.yaml

# 2. 重新初始化MAS数据库
podman exec -it matrix-postgres psql -U postgres << EOF
DROP DATABASE IF EXISTS mas;
CREATE DATABASE mas ENCODING 'UTF8' LC_COLLATE = 'C' LC_CTYPE = 'C' TEMPLATE template0;
GRANT ALL PRIVILEGES ON DATABASE mas TO mas_user;
EOF

# 3. 重启MAS容器
podman restart matrix-mas
```

---

## 🌍 DNS和域名问题

### 问题1：域名解析失败

**症状**：
- 无法访问子域名
- DNS查询超时
- 证书申请失败

**诊断命令**：
```bash
# 测试域名解析
nslookup matrix.example.com
dig matrix.example.com
# 检查DNS传播
dig @******* matrix.example.com
dig @******* matrix.example.com
```

**解决方案**：
```bash
# 1. 检查Cloudflare DNS设置
curl -X GET "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/dns_records" \
     -H "Authorization: Bearer YOUR_CLOUDFLARE_API_TOKEN"

# 2. 手动更新DNS记录
curl -X PUT "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/dns_records/RECORD_ID" \
     -H "Authorization: Bearer YOUR_CLOUDFLARE_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data '{"type":"A","name":"matrix.example.com","content":"YOUR_IP"}'

# 3. 清理本地DNS缓存
sudo systemctl flush-dns
```

### 问题2：动态IP更新失败

**症状**：
- IP地址变化后服务不可访问
- DNS记录未更新
- 更新脚本报错

**诊断命令**：
```bash
# 检查当前公网IP
curl -s ifconfig.me
# 检查RouterOS连接
python3 /opt/matrix/scripts/update-ip.py
# 查看更新日志
tail -f /opt/matrix/logs/ip-update.log
```

**解决方案**：
```bash
# 1. 手动运行IP更新脚本
cd /opt/matrix
python3 scripts/update-ip.py

# 2. 检查定时任务
crontab -l | grep update-ip

# 3. 修复脚本权限
chmod +x /opt/matrix/scripts/update-ip.py
```

---

## ⚡ 性能问题

### 问题1：服务响应缓慢

**症状**：
- 页面加载时间长
- API响应超时
- 消息发送延迟

**诊断命令**：
```bash
# 检查系统负载
top
htop
# 检查容器资源使用
podman stats
# 测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8008/_matrix/client/versions
```

**解决方案**：
```bash
# 1. 优化Synapse配置
cat >> /opt/matrix/configs/synapse/homeserver.yaml << EOF
# 性能优化
event_cache_size: 10K
caches:
  global_factor: 2.0
  per_cache_factors:
    get_users_who_share_room_with_user: 5.0
    get_users_in_room: 5.0
EOF

# 2. 增加容器资源限制
podman update --memory=2g --cpus=2 matrix-synapse

# 3. 启用Redis缓存
# 确保Redis配置正确并重启Synapse
```

### 问题2：数据库性能瓶颈

**症状**：
- 数据库查询缓慢
- 高磁盘I/O
- 连接池满

**解决方案**：
```bash
# 1. 优化数据库连接池
cat >> /opt/matrix/configs/synapse/homeserver.yaml << EOF
database:
  args:
    cp_min: 10
    cp_max: 20
    cp_reconnect: true
EOF

# 2. 添加数据库索引
podman exec -it matrix-postgres psql -U synapse_user -d synapse << EOF
CREATE INDEX CONCURRENTLY IF NOT EXISTS events_room_stream ON events(room_id, stream_ordering);
CREATE INDEX CONCURRENTLY IF NOT EXISTS events_order ON events(origin_server_ts, stream_ordering);
EOF

# 3. 定期维护数据库
cat > /opt/matrix/scripts/db-maintenance.sh << 'EOF'
#!/bin/bash
podman exec -it matrix-postgres psql -U postgres -d synapse << SQL
VACUUM ANALYZE;
REINDEX DATABASE synapse;
SQL
EOF
chmod +x /opt/matrix/scripts/db-maintenance.sh
```

---

## 👤 用户认证问题

### 问题1：无法创建用户

**症状**：
- 注册功能不可用
- 管理员无法创建用户
- 用户创建命令失败

**解决方案**：
```bash
# 1. 使用Synapse管理命令创建用户
podman exec -it matrix-synapse register_new_matrix_user \
    -c /data/homeserver.yaml \
    -u username \
    -p password \
    -a \
    http://localhost:8008

# 2. 启用用户注册（临时）
sed -i 's/enable_registration: false/enable_registration: true/' \
    /opt/matrix/configs/synapse/homeserver.yaml
podman restart matrix-synapse

# 3. 使用注册令牌
podman exec -it matrix-synapse register_new_matrix_user \
    -c /data/homeserver.yaml \
    -t YOUR_REGISTRATION_TOKEN \
    http://localhost:8008
```

### 问题2：用户登录失败

**症状**：
- 密码正确但无法登录
- 认证服务错误
- 会话过期

**诊断命令**：
```bash
# 检查用户是否存在
podman exec -it matrix-postgres psql -U synapse_user -d synapse \
    -c "SELECT name FROM users WHERE name='@username:matrix.example.com';"
# 检查认证日志
podman logs matrix-synapse | grep -i auth
```

**解决方案**：
```bash
# 1. 重置用户密码
podman exec -it matrix-synapse hash_password -p new_password
# 然后在数据库中更新密码哈希

# 2. 检查MAS配置
curl -s http://localhost:8081/.well-known/openid_configuration

# 3. 清理用户会话
podman exec -it matrix-postgres psql -U synapse_user -d synapse \
    -c "DELETE FROM access_tokens WHERE user_id='@username:matrix.example.com';"
```

---

## 📊 日志分析

### 重要日志文件位置

```bash
# 系统日志
/var/log/syslog
/var/log/nginx/error.log
/var/log/nginx/access.log

# Matrix服务日志
/opt/matrix/logs/
/opt/matrix/data/synapse/homeserver.log

# 容器日志
podman logs matrix-synapse
podman logs matrix-postgres
podman logs matrix-element
podman logs matrix-mas
podman logs matrix-redis
podman logs matrix-coturn
```

### 常用日志分析命令

```bash
# 实时查看Synapse日志
podman logs -f matrix-synapse

# 查找错误信息
podman logs matrix-synapse 2>&1 | grep -i error

# 分析访问日志
tail -f /var/log/nginx/access.log | grep -E "(matrix|chat|mas)"

# 查看系统资源使用
dmesg | grep -i "killed process"

# 分析数据库日志
podman logs matrix-postgres | grep -i error
```

### 日志轮转配置

```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/matrix << EOF
/opt/matrix/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 matrix matrix
    postrotate
        systemctl reload matrix-stack
    endscript
}
EOF
```

---

## 🚨 紧急恢复

### 完全服务恢复流程

```bash
#!/bin/bash
# 紧急恢复脚本 - 保存为 /opt/matrix/scripts/emergency-recovery.sh

echo "=== Matrix 服务紧急恢复 ==="
echo "开始时间: $(date)"

# 1. 停止所有服务
echo "停止所有服务..."
sudo systemctl stop matrix-stack
podman stop $(podman ps -aq) 2>/dev/null

# 2. 检查系统资源
echo "检查系统资源..."
df -h /opt/matrix
free -h

# 3. 清理临时文件
echo "清理临时文件..."
sudo find /tmp -name "*matrix*" -delete 2>/dev/null
sudo find /var/tmp -name "*podman*" -delete 2>/dev/null

# 4. 修复权限
echo "修复权限..."
sudo chown -R matrix:matrix /opt/matrix/
sudo chmod -R 755 /opt/matrix/configs/
sudo chmod -R 750 /opt/matrix/data/

# 5. 重新创建容器网络
echo "重新创建网络..."
podman network rm matrix-network 2>/dev/null
podman network create matrix-network 2>/dev/null

# 6. 启动核心服务
echo "启动核心服务..."
# PostgreSQL
podman run -d \
    --name matrix-postgres \
    --network host \
    -e POSTGRES_PASSWORD="${POSTGRES_PASSWORD}" \
    -v /opt/matrix/data/postgres:/var/lib/postgresql/data:Z \
    --restart unless-stopped \
    docker.io/postgres:15-alpine

sleep 15

# Redis
podman run -d \
    --name matrix-redis \
    --network host \
    -v /opt/matrix/data/redis:/data:Z \
    --restart unless-stopped \
    docker.io/redis:7-alpine

sleep 5

# Synapse
podman run -d \
    --name matrix-synapse \
    --network host \
    -v /opt/matrix/configs/synapse:/data:Z \
    -v /opt/matrix/data/synapse:/data/media_store:Z \
    --restart unless-stopped \
    docker.io/matrixdotorg/synapse:latest

sleep 10

# 7. 验证核心服务
echo "验证核心服务..."
if curl -s http://localhost:8008/_matrix/client/versions >/dev/null; then
    echo "✅ Synapse 服务正常"
else
    echo "❌ Synapse 服务异常"
fi

# 8. 启动其他服务
echo "启动其他服务..."
sudo systemctl start matrix-stack

# 9. 最终检查
echo "最终检查..."
sleep 30
/opt/matrix/scripts/quick-check.sh

echo "=== 恢复完成 ==="
echo "结束时间: $(date)"
```

### 数据备份恢复

```bash
# 从备份恢复数据库
restore_database() {
    local backup_file=$1
    echo "从备份恢复数据库: $backup_file"
    
    # 停止相关服务
    podman stop matrix-synapse matrix-mas
    
    # 清空现有数据库
    podman exec -it matrix-postgres psql -U postgres << EOF
DROP DATABASE IF EXISTS synapse;
DROP DATABASE IF EXISTS mas;
EOF
    
    # 恢复数据库
    podman exec -i matrix-postgres psql -U postgres < "$backup_file"
    
    # 重启服务
    podman start matrix-synapse matrix-mas
}

# 恢复配置文件
restore_configs() {
    local backup_file=$1
    echo "从备份恢复配置: $backup_file"
    
    # 备份当前配置
    cp -r /opt/matrix/configs /opt/matrix/configs.backup.$(date +%s)
    
    # 解压备份
    tar -xzf "$backup_file" -C /opt/matrix/
    
    # 修复权限
    sudo chown -R matrix:matrix /opt/matrix/configs/
}

# 使用示例
# restore_database "/opt/matrix/backups/database_20231201_120000.sql"
# restore_configs "/opt/matrix/backups/configs_20231201_120000.tar.gz"
```

### 联系支持

如果以上所有方法都无法解决问题，请：

1. **收集诊断信息**：
   ```bash
   /opt/matrix/scripts/quick-check.sh > /tmp/matrix-diagnosis.txt
   ```

2. **收集日志**：
   ```bash
   tar -czf /tmp/matrix-logs.tar.gz /opt/matrix/logs/ /var/log/nginx/
   ```

3. **记录问题详情**：
   - 问题发生时间
   - 具体错误信息
   - 最近的配置更改
   - 系统环境信息

4. **寻求帮助**：
   - Matrix官方社区：#matrix:matrix.org
   - GitHub Issues
   - 技术论坛

---

**注意**：在执行任何恢复操作之前，请务必备份当前数据和配置！