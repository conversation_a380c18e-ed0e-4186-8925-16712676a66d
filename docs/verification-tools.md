# Matrix 服务器验证和测试工具

## 📋 目录
1. [部署验证脚本](#部署验证脚本)
2. [连通性测试工具](#连通性测试工具)
3. [性能测试脚本](#性能测试脚本)
4. [用户验收测试清单](#用户验收测试清单)
5. [自动化测试套件](#自动化测试套件)
6. [监控和告警工具](#监控和告警工具)

---

## 🔍 部署验证脚本

### 主验证脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/deployment-verification.sh

set -e

echo "=== Matrix 服务器部署验证 ==="
echo "验证时间: $(date)"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "测试: $test_name ... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 详细测试函数
run_detailed_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "测试: $test_name"
    
    if result=$(eval "$test_command" 2>&1); then
        echo -e "${GREEN}✅ 通过${NC}"
        echo "结果: $result"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        echo
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "错误: $result"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo
        return 1
    fi
}
```# 1
. 系统环境验证
echo "=== 1. 系统环境验证 ==="
run_test "检查操作系统版本" "lsb_release -rs | grep -E '(20|22|11|12)'"
run_test "检查内存容量(>=4GB)" "[ \$(free -m | awk '/^Mem:/{print \$2}') -ge 4000 ]"
run_test "检查磁盘空间(>=20GB)" "[ \$(df /opt/matrix | tail -1 | awk '{print \$4}') -ge 20000000 ]"
run_test "检查Podman安装" "command -v podman"
run_test "检查Python3安装" "command -v python3"
run_test "检查curl安装" "command -v curl"
run_test "检查jq安装" "command -v jq"

# 2. 网络连接验证
echo "=== 2. 网络连接验证 ==="
run_test "检查互联网连接" "ping -c 1 *******"
run_test "检查DNS解析" "nslookup google.com"
run_test "检查HTTPS连接" "curl -s https://www.google.com"

# 3. 容器服务验证
echo "=== 3. 容器服务验证 ==="
containers=("matrix-postgres" "matrix-redis" "matrix-synapse" "matrix-element" "matrix-mas" "matrix-coturn")
for container in "${containers[@]}"; do
    run_test "检查容器 $container" "podman ps --format '{{.Names}}' | grep -q '^${container}\$'"
done

# 4. 端口监听验证
echo "=== 4. 端口监听验证 ==="
ports=("5432" "6379" "8008" "8080" "8081" "3478")
services=("PostgreSQL" "Redis" "Synapse" "Element" "MAS" "Coturn")
for i in "${!ports[@]}"; do
    port="${ports[$i]}"
    service="${services[$i]}"
    run_test "检查端口 $port ($service)" "netstat -tlnp | grep -q ':$port '"
done

# 5. 服务响应验证
echo "=== 5. 服务响应验证 ==="
run_detailed_test "Synapse API响应" "curl -s http://localhost:8008/_matrix/client/versions | jq -r '.versions[0]'"
run_detailed_test "Element Web响应" "curl -s -o /dev/null -w '%{http_code}' http://localhost:8080"
run_detailed_test "MAS响应" "curl -s -o /dev/null -w '%{http_code}' http://localhost:8081"

# 6. 数据库连接验证
echo "=== 6. 数据库连接验证 ==="
run_test "PostgreSQL连接" "podman exec matrix-postgres psql -U postgres -c 'SELECT 1;'"
run_test "Synapse数据库" "podman exec matrix-postgres psql -U synapse_user -d synapse -c 'SELECT 1;'"
run_test "MAS数据库" "podman exec matrix-postgres psql -U mas_user -d mas -c 'SELECT 1;'"
run_test "Redis连接" "podman exec matrix-redis redis-cli ping | grep -q PONG"

# 7. 配置文件验证
echo "=== 7. 配置文件验证 ==="
run_test "Synapse配置文件" "[ -f /opt/matrix/configs/synapse/homeserver.yaml ]"
run_test "Element配置文件" "[ -f /opt/matrix/configs/element/config.json ]"
run_test "MAS配置文件" "[ -f /opt/matrix/configs/mas/config.yaml ]"
run_test "Coturn配置文件" "[ -f /opt/matrix/configs/coturn/turnserver.conf ]"

# 8. 权限验证
echo "=== 8. 权限验证 ==="
run_test "Matrix用户权限" "[ \$(stat -c '%U' /opt/matrix) = 'matrix' ]"
run_test "配置目录权限" "[ \$(stat -c '%a' /opt/matrix/configs) = '755' ]"
run_test "数据目录权限" "[ \$(stat -c '%a' /opt/matrix/data) = '750' ]"

# 验证结果汇总
echo "=== 验证结果汇总 ==="
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有验证测试通过！部署成功！${NC}"
    exit 0
else
    echo -e "${RED}⚠️  有 $FAILED_TESTS 个测试失败，请检查相关配置${NC}"
    exit 1
fi
EOF

chmod +x /opt/matrix/scripts/deployment-verification.sh
```

### 快速健康检查脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/health-check.sh

echo "=== Matrix 服务器健康检查 ==="
echo "检查时间: $(date)"
echo

# 检查容器状态
echo "📦 容器状态:"
podman ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep matrix

echo
echo "💾 资源使用:"
echo "内存使用:"
free -h | head -2
echo "磁盘使用:"
df -h /opt/matrix | tail -1
echo "CPU负载:"
uptime

echo
echo "🌐 服务响应:"
services=("http://localhost:8008/_matrix/client/versions:Synapse" 
          "http://localhost:8080:Element" 
          "http://localhost:8081:MAS")

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-3)
    name=$(echo $service | cut -d: -f4)
    
    if response=$(curl -s -w "%{http_code}" -o /dev/null "$url" --connect-timeout 5); then
        if [[ "$response" =~ ^[23] ]]; then
            echo "✅ $name - 响应正常 ($response)"
        else
            echo "⚠️  $name - 响应异常 ($response)"
        fi
    else
        echo "❌ $name - 无响应"
    fi
done

echo
echo "=== 检查完成 ==="
EOF

chmod +x /opt/matrix/scripts/health-check.sh
```#
# 🌐 连通性测试工具

### 网络连通性测试脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/connectivity-test.sh

echo "=== Matrix 服务器连通性测试 ==="
echo "测试时间: $(date)"
echo

# 配置变量（请根据实际情况修改）
DOMAIN="example.com"
EXTERNAL_IP="YOUR_VPS_IP"
INTERNAL_IP="YOUR_INTERNAL_IP"

# 测试函数
test_connection() {
    local host="$1"
    local port="$2"
    local service="$3"
    local timeout="${4:-5}"
    
    echo -n "测试 $service ($host:$port) ... "
    if timeout $timeout bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        echo "✅ 连接成功"
        return 0
    else
        echo "❌ 连接失败"
        return 1
    fi
}

# 测试HTTP响应
test_http() {
    local url="$1"
    local service="$2"
    local expected_code="${3:-200}"
    
    echo -n "测试 $service ($url) ... "
    if response=$(curl -s -w "%{http_code}" -o /dev/null "$url" --connect-timeout 10); then
        if [[ "$response" == "$expected_code" ]] || [[ "$response" =~ ^[23] ]]; then
            echo "✅ HTTP $response"
            return 0
        else
            echo "⚠️  HTTP $response"
            return 1
        fi
    else
        echo "❌ 连接失败"
        return 1
    fi
}

# 1. 本地服务连通性测试
echo "=== 1. 本地服务连通性测试 ==="
test_connection "localhost" "5432" "PostgreSQL"
test_connection "localhost" "6379" "Redis"
test_connection "localhost" "8008" "Synapse"
test_connection "localhost" "8080" "Element Web"
test_connection "localhost" "8081" "MAS"
test_connection "localhost" "3478" "Coturn"

echo
echo "=== 2. 本地HTTP服务测试 ==="
test_http "http://localhost:8008/_matrix/client/versions" "Synapse API"
test_http "http://localhost:8080" "Element Web"
test_http "http://localhost:8081" "MAS"

echo
echo "=== 3. 外部域名解析测试 ==="
domains=("$DOMAIN" "matrix.$DOMAIN" "chat.$DOMAIN" "mas.$DOMAIN" "turn.$DOMAIN")
for domain in "${domains[@]}"; do
    echo -n "解析 $domain ... "
    if ip=$(nslookup "$domain" 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}'); then
        echo "✅ $ip"
    else
        echo "❌ 解析失败"
    fi
done

echo
echo "=== 4. 外部HTTPS连接测试 ==="
https_services=("https://$DOMAIN" "https://matrix.$DOMAIN" "https://chat.$DOMAIN" "https://mas.$DOMAIN")
for service in "${https_services[@]}"; do
    test_http "$service" "$(echo $service | cut -d/ -f3)"
done

echo
echo "=== 5. Matrix联邦测试 ==="
echo -n "测试Matrix联邦发现 ... "
if response=$(curl -s "https://$DOMAIN/.well-known/matrix/server" 2>/dev/null); then
    if echo "$response" | jq -e '.["m.server"]' >/dev/null 2>&1; then
        server=$(echo "$response" | jq -r '.["m.server"]')
        echo "✅ 发现服务器: $server"
    else
        echo "⚠️  响应格式错误: $response"
    fi
else
    echo "❌ 无法获取联邦信息"
fi

echo -n "测试Matrix客户端发现 ... "
if response=$(curl -s "https://$DOMAIN/.well-known/matrix/client" 2>/dev/null); then
    if echo "$response" | jq -e '.["m.homeserver"]' >/dev/null 2>&1; then
        homeserver=$(echo "$response" | jq -r '.["m.homeserver"]["base_url"]')
        echo "✅ 主服务器: $homeserver"
    else
        echo "⚠️  响应格式错误: $response"
    fi
else
    echo "❌ 无法获取客户端信息"
fi

echo
echo "=== 6. TURN服务器测试 ==="
echo -n "测试TURN服务器连接 ... "
if test_connection "turn.$DOMAIN" "3478" "TURN UDP" 3; then
    echo "UDP连接正常"
else
    echo "UDP连接失败"
fi

if test_connection "turn.$DOMAIN" "3478" "TURN TCP" 3; then
    echo "TCP连接正常"
else
    echo "TCP连接失败"
fi

echo
echo "=== 连通性测试完成 ==="
EOF

chmod +x /opt/matrix/scripts/connectivity-test.sh
```

### 端口扫描工具

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/port-scan.sh

echo "=== Matrix 服务器端口扫描 ==="
echo "扫描时间: $(date)"
echo

# 要扫描的端口和服务
declare -A PORTS=(
    ["5432"]="PostgreSQL数据库"
    ["6379"]="Redis缓存"
    ["8008"]="Matrix Synapse"
    ["8080"]="Element Web"
    ["8081"]="Matrix Authentication Service"
    ["3478"]="Coturn TURN服务器"
    ["80"]="HTTP服务"
    ["443"]="HTTPS服务"
    ["22"]="SSH服务"
)

# 扫描本地端口
echo "📡 本地端口扫描:"
for port in "${!PORTS[@]}"; do
    service="${PORTS[$port]}"
    echo -n "端口 $port ($service) ... "
    
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        # 获取监听进程信息
        process=$(netstat -tlnp 2>/dev/null | grep ":$port " | awk '{print $7}' | head -1)
        echo "✅ 监听中 [$process]"
    else
        echo "❌ 未监听"
    fi
done

echo
echo "🔍 详细端口信息:"
netstat -tlnp 2>/dev/null | grep -E ":(5432|6379|8008|8080|8081|3478|80|443|22) " | \
while read line; do
    echo "  $line"
done

echo
echo "🐳 容器端口映射:"
podman ps --format "table {{.Names}}\t{{.Ports}}" | grep -v "PORTS"

echo
echo "=== 端口扫描完成 ==="
EOF

chmod +x /opt/matrix/scripts/port-scan.sh
```## ⚡ 性能
测试脚本

### 系统性能测试

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/performance-test.sh

echo "=== Matrix 服务器性能测试 ==="
echo "测试时间: $(date)"
echo

# 1. 系统资源测试
echo "=== 1. 系统资源测试 ==="
echo "CPU信息:"
lscpu | grep -E "(Model name|CPU\(s\)|Thread|Core)"
echo
echo "内存信息:"
free -h
echo
echo "磁盘信息:"
df -h /opt/matrix
echo
echo "系统负载:"
uptime
echo

# 2. 磁盘I/O性能测试
echo "=== 2. 磁盘I/O性能测试 ==="
echo "测试磁盘写入性能..."
write_speed=$(dd if=/dev/zero of=/tmp/test_write bs=1M count=100 2>&1 | grep -o '[0-9.]\+ MB/s' | tail -1)
echo "磁盘写入速度: $write_speed"

echo "测试磁盘读取性能..."
read_speed=$(dd if=/tmp/test_write of=/dev/null bs=1M 2>&1 | grep -o '[0-9.]\+ MB/s' | tail -1)
echo "磁盘读取速度: $read_speed"
rm -f /tmp/test_write

# 3. 网络性能测试
echo "=== 3. 网络性能测试 ==="
echo "测试网络延迟:"
ping -c 5 ******* | tail -1

echo "测试DNS解析速度:"
time nslookup google.com >/dev/null 2>&1

# 4. 数据库性能测试
echo "=== 4. 数据库性能测试 ==="
echo "PostgreSQL连接测试:"
time podman exec matrix-postgres psql -U postgres -c "SELECT 1;" >/dev/null 2>&1
echo "连接时间: $(echo $?)"

echo "数据库查询性能测试:"
podman exec matrix-postgres psql -U synapse_user -d synapse -c "
EXPLAIN ANALYZE SELECT COUNT(*) FROM events WHERE room_id = 'test';
" 2>/dev/null | grep "Execution Time" || echo "查询测试完成"

# 5. Redis性能测试
echo "=== 5. Redis性能测试 ==="
echo "Redis连接测试:"
redis_ping=$(podman exec matrix-redis redis-cli ping 2>/dev/null)
echo "Redis响应: $redis_ping"

echo "Redis性能基准测试:"
podman exec matrix-redis redis-cli --latency-history -i 1 2>/dev/null &
REDIS_PID=$!
sleep 5
kill $REDIS_PID 2>/dev/null

# 6. HTTP服务性能测试
echo "=== 6. HTTP服务性能测试 ==="

# 创建curl格式文件
cat > /tmp/curl-format.txt << 'EOF'
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
EOF

echo "Synapse API响应时间:"
curl -w "@/tmp/curl-format.txt" -o /dev/null -s "http://localhost:8008/_matrix/client/versions"

echo
echo "Element Web响应时间:"
curl -w "@/tmp/curl-format.txt" -o /dev/null -s "http://localhost:8080"

echo
echo "MAS响应时间:"
curl -w "@/tmp/curl-format.txt" -o /dev/null -s "http://localhost:8081"

rm -f /tmp/curl-format.txt

# 7. 容器资源使用测试
echo "=== 7. 容器资源使用测试 ==="
echo "容器资源统计:"
podman stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"

echo
echo "=== 性能测试完成 ==="
EOF

chmod +x /opt/matrix/scripts/performance-test.sh
```

### 负载测试脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/load-test.sh

echo "=== Matrix 服务器负载测试 ==="
echo "测试时间: $(date)"
echo "警告: 此测试会对服务器产生负载，请在测试环境中运行"
echo

# 配置参数
CONCURRENT_USERS=10
TEST_DURATION=60
API_ENDPOINT="http://localhost:8008/_matrix/client/versions"

echo "测试参数:"
echo "- 并发用户数: $CONCURRENT_USERS"
echo "- 测试持续时间: ${TEST_DURATION}秒"
echo "- 测试端点: $API_ENDPOINT"
echo

# 创建负载测试函数
run_load_test() {
    local endpoint="$1"
    local name="$2"
    
    echo "开始测试 $name ..."
    
    # 创建临时结果文件
    local result_file="/tmp/load_test_$$"
    
    # 启动并发请求
    for i in $(seq 1 $CONCURRENT_USERS); do
        {
            local success=0
            local total=0
            local start_time=$(date +%s)
            
            while [ $(($(date +%s) - start_time)) -lt $TEST_DURATION ]; do
                if curl -s -o /dev/null -w "%{http_code}" "$endpoint" --connect-timeout 5 | grep -q "^2"; then
                    success=$((success + 1))
                fi
                total=$((total + 1))
                sleep 0.1
            done
            
            echo "$success $total" >> "$result_file"
        } &
    done
    
    # 等待所有测试完成
    wait
    
    # 计算结果
    local total_success=0
    local total_requests=0
    
    while read success total; do
        total_success=$((total_success + success))
        total_requests=$((total_requests + total))
    done < "$result_file"
    
    local success_rate=0
    if [ $total_requests -gt 0 ]; then
        success_rate=$((total_success * 100 / total_requests))
    fi
    
    local rps=$((total_requests / TEST_DURATION))
    
    echo "测试结果 - $name:"
    echo "  总请求数: $total_requests"
    echo "  成功请求数: $total_success"
    echo "  成功率: ${success_rate}%"
    echo "  平均RPS: $rps"
    echo
    
    rm -f "$result_file"
}

# 执行负载测试
run_load_test "http://localhost:8008/_matrix/client/versions" "Synapse API"
run_load_test "http://localhost:8080" "Element Web"
run_load_test "http://localhost:8081" "MAS"

echo "=== 负载测试完成 ==="
EOF

chmod +x /opt/matrix/scripts/load-test.sh
```#
# ✅ 用户验收测试清单

### 基础功能测试清单

```markdown
# Matrix 服务器用户验收测试清单

## 📋 测试环境准备
- [ ] 确认所有服务已启动
- [ ] 确认网络连接正常
- [ ] 准备测试用户账号
- [ ] 准备不同类型的客户端（Web、移动端）

## 🔐 用户认证测试
- [ ] 管理员用户登录测试
  - [ ] 使用正确密码登录成功
  - [ ] 使用错误密码登录失败
  - [ ] 登录后显示正确的用户信息
- [ ] 普通用户注册测试（如果启用）
  - [ ] 注册新用户成功
  - [ ] 重复用户名注册失败
  - [ ] 弱密码注册失败
- [ ] 用户注销测试
  - [ ] 注销后无法访问受保护资源
  - [ ] 重新登录正常

## 💬 基础通信功能测试
- [ ] 房间创建和管理
  - [ ] 创建公开房间
  - [ ] 创建私有房间
  - [ ] 设置房间名称和描述
  - [ ] 邀请用户加入房间
  - [ ] 踢出用户
  - [ ] 设置房间权限
- [ ] 消息发送和接收
  - [ ] 发送文本消息
  - [ ] 发送表情符号
  - [ ] 发送格式化消息（粗体、斜体等）
  - [ ] 消息实时同步
  - [ ] 消息历史记录查看
- [ ] 文件分享功能
  - [ ] 上传图片文件
  - [ ] 上传文档文件
  - [ ] 下载分享的文件
  - [ ] 文件大小限制测试

## 🎥 音视频通话测试
- [ ] 语音通话功能
  - [ ] 发起语音通话
  - [ ] 接听语音通话
  - [ ] 通话质量正常
  - [ ] 正常挂断通话
- [ ] 视频通话功能
  - [ ] 发起视频通话
  - [ ] 接听视频通话
  - [ ] 视频画面正常
  - [ ] 音视频同步正常
- [ ] 群组通话功能
  - [ ] 多人语音通话
  - [ ] 多人视频通话
  - [ ] 屏幕分享功能

## 🌐 联邦功能测试
- [ ] 跨服务器通信
  - [ ] 与其他Matrix服务器用户聊天
  - [ ] 加入其他服务器的公开房间
  - [ ] 邀请其他服务器用户
- [ ] 服务器发现
  - [ ] .well-known配置正确
  - [ ] 联邦身份验证正常

## 📱 客户端兼容性测试
- [ ] Element Web客户端
  - [ ] 登录功能正常
  - [ ] 界面显示正常
  - [ ] 所有功能可用
- [ ] Element移动客户端
  - [ ] Android客户端连接正常
  - [ ] iOS客户端连接正常
  - [ ] 推送通知正常
- [ ] 第三方客户端
  - [ ] FluffyChat连接测试
  - [ ] Nheko连接测试

## 🔒 安全功能测试
- [ ] 端到端加密
  - [ ] 加密房间创建
  - [ ] 加密消息发送
  - [ ] 密钥验证功能
  - [ ] 设备管理功能
- [ ] 权限管理
  - [ ] 房间管理员权限
  - [ ] 普通用户权限限制
  - [ ] 服务器管理员权限

## 🚀 性能测试
- [ ] 响应速度测试
  - [ ] 登录响应时间 < 3秒
  - [ ] 消息发送延迟 < 1秒
  - [ ] 房间加载时间 < 5秒
- [ ] 并发用户测试
  - [ ] 10个用户同时在线
  - [ ] 50个用户同时在线（如果资源允许）
- [ ] 大文件传输测试
  - [ ] 10MB文件上传
  - [ ] 50MB文件上传（如果配置允许）

## 🔧 管理功能测试
- [ ] 用户管理
  - [ ] 创建新用户
  - [ ] 重置用户密码
  - [ ] 禁用用户账号
  - [ ] 删除用户账号
- [ ] 房间管理
  - [ ] 查看所有房间
  - [ ] 删除房间
  - [ ] 房间统计信息
- [ ] 服务器统计
  - [ ] 用户数量统计
  - [ ] 房间数量统计
  - [ ] 消息数量统计

## 🛠️ 故障恢复测试
- [ ] 服务重启测试
  - [ ] 重启后服务自动恢复
  - [ ] 数据完整性保持
  - [ ] 用户会话保持
- [ ] 网络中断测试
  - [ ] 网络恢复后自动重连
  - [ ] 离线消息同步
- [ ] 数据库故障测试
  - [ ] 数据库重启后服务恢复
  - [ ] 数据一致性检查

## 📊 测试结果记录
测试日期: ___________
测试人员: ___________
测试环境: ___________

### 测试结果汇总
- 总测试项目: _____ 项
- 通过项目: _____ 项
- 失败项目: _____ 项
- 成功率: _____%

### 主要问题记录
1. _________________________________
2. _________________________________
3. _________________________________

### 改进建议
1. _________________________________
2. _________________________________
3. _________________________________

测试结论: □ 通过验收 □ 需要修复后重测
```

### 自动化验收测试脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/acceptance-test.sh

echo "=== Matrix 服务器自动化验收测试 ==="
echo "测试时间: $(date)"
echo

# 测试配置
DOMAIN="example.com"
TEST_USER="testuser"
TEST_PASSWORD="TestPassword123!"
ADMIN_USER="admin"
ADMIN_PASSWORD="AdminPassword123!"

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_acceptance_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "验收测试: $test_name ... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo "✅ 通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo "❌ 失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 1. 基础服务验收测试
echo "=== 1. 基础服务验收测试 ==="
run_acceptance_test "Synapse API可访问" "curl -s http://localhost:8008/_matrix/client/versions | jq -e '.versions'"
run_acceptance_test "Element Web可访问" "curl -s -o /dev/null -w '%{http_code}' http://localhost:8080 | grep -q '^2'"
run_acceptance_test "MAS可访问" "curl -s -o /dev/null -w '%{http_code}' http://localhost:8081 | grep -q '^2'"

# 2. 用户认证验收测试
echo "=== 2. 用户认证验收测试 ==="
run_acceptance_test "管理员用户存在" "podman exec matrix-postgres psql -U synapse_user -d synapse -c \"SELECT name FROM users WHERE name='@${ADMIN_USER}:${DOMAIN}';\" | grep -q '@'"

# 3. 联邦功能验收测试
echo "=== 3. 联邦功能验收测试 ==="
run_acceptance_test "Matrix服务器发现配置" "curl -s https://${DOMAIN}/.well-known/matrix/server | jq -e '.\"m.server\"'"
run_acceptance_test "Matrix客户端发现配置" "curl -s https://${DOMAIN}/.well-known/matrix/client | jq -e '.\"m.homeserver\"'"

# 4. 数据持久化验收测试
echo "=== 4. 数据持久化验收测试 ==="
run_acceptance_test "PostgreSQL数据目录存在" "[ -d /opt/matrix/data/postgres ] && [ \$(ls -A /opt/matrix/data/postgres | wc -l) -gt 0 ]"
run_acceptance_test "Synapse媒体目录存在" "[ -d /opt/matrix/data/synapse ]"
run_acceptance_test "Redis数据目录存在" "[ -d /opt/matrix/data/redis ]"

# 5. 配置文件验收测试
echo "=== 5. 配置文件验收测试 ==="
run_acceptance_test "Synapse配置有效" "python3 -c 'import yaml; yaml.safe_load(open(\"/opt/matrix/configs/synapse/homeserver.yaml\"))'"
run_acceptance_test "Element配置有效" "jq . /opt/matrix/configs/element/config.json >/dev/null"
run_acceptance_test "MAS配置有效" "python3 -c 'import yaml; yaml.safe_load(open(\"/opt/matrix/configs/mas/config.yaml\"))'"

# 6. 安全配置验收测试
echo "=== 6. 安全配置验收测试 ==="
run_acceptance_test "用户注册默认关闭" "grep -q 'enable_registration: false' /opt/matrix/configs/synapse/homeserver.yaml"
run_acceptance_test "数据目录权限正确" "[ \$(stat -c '%a' /opt/matrix/data) = '750' ]"
run_acceptance_test "配置文件权限正确" "[ \$(stat -c '%a' /opt/matrix/configs) = '755' ]"

# 验收测试结果汇总
echo
echo "=== 验收测试结果汇总 ==="
echo "总测试数: $TOTAL_TESTS"
echo "通过测试: $PASSED_TESTS"
echo "失败测试: $FAILED_TESTS"

success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo "成功率: ${success_rate}%"

if [ $success_rate -ge 90 ]; then
    echo "🎉 验收测试通过！系统可以投入使用。"
    exit 0
elif [ $success_rate -ge 70 ]; then
    echo "⚠️  验收测试基本通过，但有部分问题需要关注。"
    exit 1
else
    echo "❌ 验收测试失败，系统需要修复后重新测试。"
    exit 2
fi
EOF

chmod +x /opt/matrix/scripts/acceptance-test.sh
```## 🤖 自动化测试
套件

### 完整测试套件脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/test-suite.sh

echo "=== Matrix 服务器完整测试套件 ==="
echo "开始时间: $(date)"
echo

# 创建测试报告目录
REPORT_DIR="/opt/matrix/test-reports/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$REPORT_DIR"

# 测试结果汇总
SUITE_RESULTS="$REPORT_DIR/suite-summary.txt"
echo "Matrix 服务器测试套件报告" > "$SUITE_RESULTS"
echo "测试时间: $(date)" >> "$SUITE_RESULTS"
echo "==============================" >> "$SUITE_RESULTS"

# 运行测试函数
run_test_script() {
    local script_name="$1"
    local script_path="$2"
    local report_file="$REPORT_DIR/${script_name}.log"
    
    echo "运行测试: $script_name"
    echo "开始时间: $(date)" > "$report_file"
    
    if [ -x "$script_path" ]; then
        if "$script_path" >> "$report_file" 2>&1; then
            echo "✅ $script_name - 通过" | tee -a "$SUITE_RESULTS"
            return 0
        else
            echo "❌ $script_name - 失败" | tee -a "$SUITE_RESULTS"
            return 1
        fi
    else
        echo "⚠️  $script_name - 脚本不存在或无执行权限" | tee -a "$SUITE_RESULTS"
        return 1
    fi
}

# 执行所有测试
echo "开始执行测试套件..."
echo

# 1. 部署验证测试
run_test_script "部署验证测试" "/opt/matrix/scripts/deployment-verification.sh"

# 2. 健康检查测试
run_test_script "健康检查测试" "/opt/matrix/scripts/health-check.sh"

# 3. 连通性测试
run_test_script "连通性测试" "/opt/matrix/scripts/connectivity-test.sh"

# 4. 端口扫描测试
run_test_script "端口扫描测试" "/opt/matrix/scripts/port-scan.sh"

# 5. 性能测试
run_test_script "性能测试" "/opt/matrix/scripts/performance-test.sh"

# 6. 验收测试
run_test_script "验收测试" "/opt/matrix/scripts/acceptance-test.sh"

# 生成HTML报告
generate_html_report() {
    local html_report="$REPORT_DIR/test-report.html"
    
    cat > "$html_report" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix 服务器测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { color: green; }
        .fail { color: red; }
        .warn { color: orange; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Matrix 服务器测试报告</h1>
        <p>生成时间: $(date)</p>
        <p>测试环境: $(hostname)</p>
    </div>
    
    <div class="test-section">
        <h2>测试汇总</h2>
        <pre>$(cat "$SUITE_RESULTS")</pre>
    </div>
EOF

    # 添加各个测试的详细结果
    for log_file in "$REPORT_DIR"/*.log; do
        if [ -f "$log_file" ]; then
            test_name=$(basename "$log_file" .log)
            cat >> "$html_report" << EOF
    <div class="test-section">
        <h2>$test_name</h2>
        <pre>$(cat "$log_file")</pre>
    </div>
EOF
        fi
    done
    
    cat >> "$html_report" << EOF
</body>
</html>
EOF

    echo "HTML报告已生成: $html_report"
}

# 生成报告
generate_html_report

# 测试套件完成
echo
echo "=== 测试套件执行完成 ==="
echo "结束时间: $(date)"
echo "测试报告目录: $REPORT_DIR"
echo "查看汇总报告: cat $SUITE_RESULTS"
echo "查看HTML报告: $REPORT_DIR/test-report.html"

# 返回适当的退出码
if grep -q "❌" "$SUITE_RESULTS"; then
    echo "⚠️  部分测试失败，请查看详细报告"
    exit 1
else
    echo "🎉 所有测试通过！"
    exit 0
fi
EOF

chmod +x /opt/matrix/scripts/test-suite.sh
```

### 持续监控脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/continuous-monitor.sh

echo "=== Matrix 服务器持续监控 ==="
echo "监控开始时间: $(date)"
echo

# 配置参数
MONITOR_INTERVAL=300  # 5分钟检查一次
LOG_FILE="/opt/matrix/logs/monitor.log"
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEM=85
ALERT_THRESHOLD_DISK=90

# 创建日志文件
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查系统资源
check_system_resources() {
    # CPU使用率
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    cpu_usage=${cpu_usage%.*}  # 去掉小数部分
    
    # 内存使用率
    mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    # 磁盘使用率
    disk_usage=$(df /opt/matrix | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    
    log_message "系统资源 - CPU: ${cpu_usage}%, 内存: ${mem_usage}%, 磁盘: ${disk_usage}%"
    
    # 检查阈值
    if [ "$cpu_usage" -gt "$ALERT_THRESHOLD_CPU" ]; then
        log_message "警告: CPU使用率过高 (${cpu_usage}%)"
    fi
    
    if [ "$mem_usage" -gt "$ALERT_THRESHOLD_MEM" ]; then
        log_message "警告: 内存使用率过高 (${mem_usage}%)"
    fi
    
    if [ "$disk_usage" -gt "$ALERT_THRESHOLD_DISK" ]; then
        log_message "警告: 磁盘使用率过高 (${disk_usage}%)"
    fi
}

# 检查容器状态
check_containers() {
    containers=("matrix-postgres" "matrix-redis" "matrix-synapse" "matrix-element" "matrix-mas" "matrix-coturn")
    
    for container in "${containers[@]}"; do
        if podman ps --format "{{.Names}}" | grep -q "^${container}$"; then
            log_message "容器状态 - $container: 运行中"
        else
            log_message "错误: 容器 $container 未运行"
            # 尝试重启容器
            log_message "尝试重启容器: $container"
            podman start "$container" && log_message "容器 $container 重启成功" || log_message "容器 $container 重启失败"
        fi
    done
}

# 检查服务响应
check_services() {
    services=("http://localhost:8008/_matrix/client/versions:Synapse" 
              "http://localhost:8080:Element" 
              "http://localhost:8081:MAS")
    
    for service in "${services[@]}"; do
        url=$(echo $service | cut -d: -f1-3)
        name=$(echo $service | cut -d: -f4)
        
        if curl -s -o /dev/null -w "%{http_code}" "$url" --connect-timeout 10 | grep -q "^[23]"; then
            log_message "服务状态 - $name: 正常"
        else
            log_message "错误: 服务 $name 无响应"
        fi
    done
}

# 检查数据库连接
check_database() {
    if podman exec matrix-postgres psql -U postgres -c "SELECT 1;" >/dev/null 2>&1; then
        log_message "数据库状态 - PostgreSQL: 正常"
    else
        log_message "错误: PostgreSQL 连接失败"
    fi
    
    if podman exec matrix-redis redis-cli ping >/dev/null 2>&1; then
        log_message "数据库状态 - Redis: 正常"
    else
        log_message "错误: Redis 连接失败"
    fi
}

# 主监控循环
main_monitor_loop() {
    log_message "开始持续监控"
    
    while true; do
        log_message "=== 开始监控检查 ==="
        
        check_system_resources
        check_containers
        check_services
        check_database
        
        log_message "=== 监控检查完成 ==="
        log_message ""
        
        sleep "$MONITOR_INTERVAL"
    done
}

# 信号处理
cleanup() {
    log_message "收到停止信号，结束监控"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 启动监控
main_monitor_loop
EOF

chmod +x /opt/matrix/scripts/continuous-monitor.sh
```

## 📊 监控和告警工具

### 系统监控仪表板脚本

```bash
#!/bin/bash
# 保存为 /opt/matrix/scripts/dashboard.sh

# 清屏并显示仪表板
clear
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                          Matrix 服务器监控仪表板                              ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo

# 系统信息
echo "🖥️  系统信息:"
echo "   主机名: $(hostname)"
echo "   系统: $(lsb_release -d | cut -f2)"
echo "   内核: $(uname -r)"
echo "   运行时间: $(uptime -p)"
echo

# 资源使用情况
echo "📊 资源使用:"
echo "   CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')"
echo "   内存使用: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
echo "   磁盘使用: $(df -h /opt/matrix | tail -1 | awk '{print $3 "/" $2 " (" $5 ")"}')"
echo

# 容器状态
echo "🐳 容器状态:"
podman ps --format "   {{.Names}}: {{.Status}}" | grep matrix

echo
# 服务状态
echo "🌐 服务状态:"
services=("http://localhost:8008/_matrix/client/versions:Synapse API" 
          "http://localhost:8080:Element Web" 
          "http://localhost:8081:MAS")

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-3)
    name=$(echo $service | cut -d: -f4-)
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" --connect-timeout 5 | grep -q "^[23]"; then
        echo "   ✅ $name"
    else
        echo "   ❌ $name"
    fi
done

echo
echo "📈 实时统计 (按 Ctrl+C 退出):"
echo "   更新时间: $(date)"

# 实时更新循环
while true; do
    sleep 5
    # 移动光标到统计行并更新
    tput cup $(($(tput lines) - 1)) 0
    echo "   更新时间: $(date)                    "
done
EOF

chmod +x /opt/matrix/scripts/dashboard.sh
```

### 使用说明

```bash
# 1. 运行完整测试套件
/opt/matrix/scripts/test-suite.sh

# 2. 快速健康检查
/opt/matrix/scripts/health-check.sh

# 3. 部署验证
/opt/matrix/scripts/deployment-verification.sh

# 4. 连通性测试
/opt/matrix/scripts/connectivity-test.sh

# 5. 性能测试
/opt/matrix/scripts/performance-test.sh

# 6. 验收测试
/opt/matrix/scripts/acceptance-test.sh

# 7. 启动持续监控
/opt/matrix/scripts/continuous-monitor.sh &

# 8. 查看监控仪表板
/opt/matrix/scripts/dashboard.sh

# 9. 端口扫描
/opt/matrix/scripts/port-scan.sh
```

所有脚本都已创建完成，提供了全面的验证和测试工具集合，包括：

1. **部署验证脚本** - 验证系统环境、容器状态、服务响应等
2. **连通性测试工具** - 测试网络连接、端口监听、HTTP响应等
3. **性能测试脚本** - 系统资源、磁盘I/O、网络性能等测试
4. **用户验收测试清单** - 完整的功能测试检查清单
5. **自动化测试套件** - 集成所有测试的完整套件
6. **监控和告警工具** - 持续监控和实时仪表板

这些工具可以帮助用户全面验证Matrix服务器的部署状态和运行质量。