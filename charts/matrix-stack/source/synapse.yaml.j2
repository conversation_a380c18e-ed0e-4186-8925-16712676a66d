{#
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}

{% import 'sub_schema_values.yaml.j2' as sub_schema_values -%}
{% import 'synapse_sub_schema_values.yaml.j2' as synapse_sub_schema_values -%}
enabled: true

## A hook job will make sure that Synapse config is valid before continuing
checkConfigHook:
  enabled: true
  {{- sub_schema_values.labels() | indent(2) }}
  {{- sub_schema_values.workloadAnnotations() | indent(2) }}
  {{- sub_schema_values.serviceAccount() | indent(2) }}

{{- sub_schema_values.postgresLibPQ() }}
## Configures the media store for Synapse
media:
{{- sub_schema_values.persistentVolumeClaim("storage") | indent(2) }}

  ## The maximum size (in bytes ending in M or K) that Synapse will accept for media uploads
  ## You may need to adjust your ingress controller to also allow uploads of this size
  maxUploadSize: 100M

{{- sub_schema_values.credential("Key used to sign events and federation requests.\n## This needs to be the full signing key starting `ed25519 ...`", "signingKey", initIfAbsent=True) }}
{{- sub_schema_values.credential("Shared Secret to registering users without having any users provisioned", "registrationSharedSecret", initIfAbsent=True) }}
{{- sub_schema_values.credential("Secret used to sign Synapse issued tokens", "macaroon", initIfAbsent=True) }}

## Additional configuration to provide to all Synapse processes.
## Each key under additional is an additional config to merge into synapse homeserver.yaml
## Full details on available configuration options can be found at https://element-hq.github.io/synapse/latest/usage/configuration/config_documentation.html
{{- sub_schema_values.additionalConfig() }}

## Details of Application Service registration files to give to Synapse
## e.g.
## appservices:
## - configMap: test-appservice
##   configMapKey: registration.yaml
## - secret: test-appservice
##   secretKey: registration.yaml
appservices: []

## Additional Synapse processes managed by this chart
## e.g.
## workers:
##   client-reader:
##     enabled: true
##     replicas: 2
##   event-creator:
##     enabled: true
workers:
{{- synapse_sub_schema_values.single_worker('appservice') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('background') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('client-reader') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('encryption') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('event-creator') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('event-persister') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('federation-inbound') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('federation-reader') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('federation-sender') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('initial-synchrotron') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('media-repository') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('presence-writer') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('push-rules') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('pusher') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('receipts-account') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('sliding-sync') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('sso-login') | indent(2) }}
{{- synapse_sub_schema_values.scalable_worker('synchrotron') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('typing-persister') | indent(2) }}
{{- synapse_sub_schema_values.single_worker('user-dir') | indent(2) }}

## Synapse's logging settings
logging:
  ## The maximum level of Synapse log output before any overrides
  rootLevel: INFO

  ## Override the log level of specific loggers
  ## e.g.
  ## levelOverrides:
  ##   synapse.util.caches.lrucache: WARNING
  levelOverrides: {}
{{- sub_schema_values.image(registry='ghcr.io', repository='element-hq/synapse', tag='v1.133.0') }}
{{- sub_schema_values.ingress() }}
{{- sub_schema_values.labels() }}
{{- sub_schema_values.workloadAnnotations() }}
{{- sub_schema_values.containersSecurityContext() }}
{{- sub_schema_values.extraEnv() }}
{{- sub_schema_values.hostAliases() }}
{{- sub_schema_values.nodeSelector() }}
{{- sub_schema_values.podSecurityContext(user_id='10091', group_id='10091') }}
{{- sub_schema_values.resources(requests_memory='100Mi', requests_cpu='100m', limits_memory='4Gi') }}
{{- sub_schema_values.serviceAccount() }}
{{- sub_schema_values.serviceMonitors() }}
{{- sub_schema_values.tolerations() }}
{{- sub_schema_values.topologySpreadConstraints() }}
{{- sub_schema_values.probe("liveness", failureThreshold=8, periodSeconds=6, timeoutSeconds=2) }}
{{- sub_schema_values.probe("readiness", failureThreshold=8, periodSeconds=2, successThreshold=2, timeoutSeconds=2) }}
{{- sub_schema_values.probe("startup", failureThreshold=54, periodSeconds=2) }}

## Extra command line arguments to provide to Synapse
extraArgs: []

redis:
{{- sub_schema_values.image(registry='docker.io', repository='library/redis', tag='7.4-alpine') | indent(2) }}
{{- sub_schema_values.labels() | indent(2) }}
{{- sub_schema_values.workloadAnnotations() | indent(2) }}
{{- sub_schema_values.containersSecurityContext() | indent(2) }}
{{- sub_schema_values.extraEnv() | indent(2) }}
{{- sub_schema_values.nodeSelector() | indent(2) }}
{{- sub_schema_values.podSecurityContext(user_id='10002', group_id='10002') | indent(2) }}
{{- sub_schema_values.resources(requests_memory='50Mi', requests_cpu='50m', limits_memory='50Mi') | indent(2) }}
{{- sub_schema_values.serviceAccount() | indent(2) }}
{{- sub_schema_values.tolerations() | indent(2) }}
{{- sub_schema_values.probe("liveness") | indent(2) }}
{{- sub_schema_values.probe("readiness") | indent(2) }}
{{- sub_schema_values.probe("startup", failureThreshold=5) | indent(2) }}
