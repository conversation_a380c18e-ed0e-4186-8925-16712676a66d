{#
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}

{% import 'sub_schema_values.yaml.j2' as sub_schema_values -%}
enabled: true

# LiveKit Authentication Configuration
# This section allows you to configure authentication for the LiveKit SFU.
# You can either use an existing keys.yaml file or provide a key and secret.
# livekitAuth:
  {{- sub_schema_values.credential("The keys.yaml file for the LiveKit SFU\n## This is required if `sfu.enabled` is set to `false`", "keysYaml", initIfAbsent=False, commented=True) | indent(2) }}
  ## Provide a key and secret if not using an existing keys.yaml
  # key: ""
  {#- We set `initSecret=false` because we are describing the mechanism in the comment parameter #}
  {{- sub_schema_values.credential("The secret for the LiveKit SFU.\n## This is required if `sfu.enabled` and `keysYaml` is not used. It will be generated by the `initSecrets` job if it is empty", "secret", initIfAbsent=False, commented=True) | indent(2) }}

replicas: 1
{{- sub_schema_values.ingress() }}
{{- sub_schema_values.image(registry='ghcr.io', repository='element-hq/lk-jwt-service', tag='0.2.3') }}
{{- sub_schema_values.labels() }}
{{- sub_schema_values.workloadAnnotations() }}
{{- sub_schema_values.containersSecurityContext() }}
{{- sub_schema_values.hostAliases() }}
{{- sub_schema_values.extraEnv() }}
{{- sub_schema_values.nodeSelector() }}
{{- sub_schema_values.podSecurityContext(user_id='10033', group_id='10033') }}
{{- sub_schema_values.resources(requests_memory='20Mi', requests_cpu='50m', limits_memory='20Mi') }}
{{- sub_schema_values.serviceMonitors() }}
{{- sub_schema_values.serviceAccount() }}
{{- sub_schema_values.tolerations() }}
{{- sub_schema_values.probe("liveness") }}
{{- sub_schema_values.probe("readiness") }}
{{- sub_schema_values.probe("startup") }}

sfu:
  enabled: true

  # LiveKit Logging level
  logging:
    # log level, valid values: debug, info, warn, error
    level: info
    # log level for pion, default error
    pionLevel: error
    # when set to true, emit json fields
    json: false

  ## Additional configuration to provide to all LiveKit processes.
  ## This should be provided as yaml-string and will be merged into the default configuration.
  ## Each key under additional is an additional config to merge into LiveKit config.yaml
  ## Full details on available configuration options can be found at https://docs.livekit.io/home/<USER>/deployment/#configuration
  {{- sub_schema_values.additionalConfig() | indent(2) }}

  # Whether to start the SFU in host network mode or not
  hostNetwork: false
  exposedServices:
    {{- sub_schema_values.exposedServicePort('rtcTcp', 30881, 'NodePort') | indent(4) }}
    {{- sub_schema_values.exposedServicePort('rtcMuxedUdp', 30882, 'NodePort') | indent(4) }}
    {{- sub_schema_values.exposedServicePortRange('rtcUdp', 31000, 32000, 'NodePort') | indent(4) }}


{{- sub_schema_values.image(registry='docker.io', repository='livekit/livekit-server', tag='v1.9.0') | indent(2) }}
{{- sub_schema_values.extraEnv() | indent(2) }}
{{- sub_schema_values.labels() | indent(2) }}
{{- sub_schema_values.workloadAnnotations() | indent(2) }}
{{- sub_schema_values.containersSecurityContext() | indent(2) }}
{{- sub_schema_values.nodeSelector() | indent(2) }}
{{- sub_schema_values.podSecurityContext(user_id='10030', group_id='10030') | indent(2) }}
{{- sub_schema_values.resources(requests_memory='150Mi', requests_cpu='100m', limits_memory='4Gi') | indent(2) }}
{{- sub_schema_values.serviceAccount() | indent(2) }}
{{- sub_schema_values.serviceMonitors() | indent(2) }}
{{- sub_schema_values.tolerations() | indent(2) }}
{{- sub_schema_values.probe("liveness") | indent(2) }}
{{- sub_schema_values.probe("readiness") | indent(2) }}
{{- sub_schema_values.probe("startup") | indent(2) }}
